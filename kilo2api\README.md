# KiloCode AI OpenAI Compatible API

## 📋 项目概述

这是一个将KiloCode AI服务转换为OpenAI兼容API格式的适配器，支持在CherryStudio等客户端中使用KiloCode AI的各种模型。

## 🏗️ 架构说明

```
客户端 → KiloCode API适配器 → KiloCode AI服务
```

## 📁 文件结构

```
kilo2api/
├── main.py                 # 主程序
├── requirements.txt        # Python依赖
├── kilocode.json          # KiloCode JWT配置
├── client_api_keys.json   # 客户端API密钥
├── models.json            # 可用模型列表
├── start_kilo_api.bat     # 启动脚本
├── test_kilo_api.bat      # 测试脚本
└── README.md              # 说明文档
```

## ⚙️ 配置文件

### 1. kilocode.json - KiloCode JWT配置

```json
[
  {
    "jwt": "your_kilocode_jwt_token_here"
  }
]
```

### 2. client_api_keys.json - 客户端API密钥

```json
[
  "sk-kilo-api-key"
]
```

### 3. models.json - 可用模型列表

包含所有KiloCode支持的模型，如：
- `anthropic/claude-3.5-sonnet`
- `openai/gpt-4o`
- `google/gemini-2.5-pro`
- 等等...

## 🚀 安装和启动

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置JWT

将您的KiloCode JWT令牌添加到 `kilocode.json` 文件中。

### 3. 启动服务

#### 方法一：使用批处理脚本
```bash
start_kilo_api.bat
```

#### 方法二：直接运行
```bash
python main.py
```

服务将在 `http://localhost:8001` 启动。

## 🧪 测试验证

### 使用测试脚本
```bash
test_kilo_api.bat
```

### 手动测试

#### 测试模型列表
```bash
curl -H "Authorization: Bearer sk-kilo-api-key" http://localhost:8001/v1/models
```

#### 测试聊天接口
```bash
curl -X POST http://localhost:8001/v1/chat/completions \
  -H "Authorization: Bearer sk-kilo-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic/claude-3.5-sonnet",
    "messages": [{"role": "user", "content": "你好"}],
    "stream": false
  }'
```

## 🍒 CherryStudio配置

在CherryStudio中添加自定义API提供商：

```
Provider Name: KiloCode AI
API Type: OpenAI Compatible
Base URL: http://localhost:8001/v1
API Key: sk-kilo-api-key
```

## 📊 支持的模型

该适配器支持KiloCode AI平台上的所有模型，包括：

### Anthropic Claude系列
- `anthropic/claude-sonnet-4`
- `anthropic/claude-3.7-sonnet`
- `anthropic/claude-3.5-sonnet`
- `anthropic/claude-3.5-haiku`

### OpenAI系列
- `openai/gpt-4.1`
- `openai/gpt-4o`
- `openai/o3-pro`
- `openai/o3`
- `openai/o1`

### Google Gemini系列
- `google/gemini-2.5-pro`
- `google/gemini-2.5-flash`

### 其他模型
- `x-ai/grok-3`
- `deepseek/deepseek-r1`
- 等等...

## 🔧 API端点

### GET /v1/models
获取可用模型列表

### POST /v1/chat/completions
创建聊天完成，支持：
- 流式和非流式响应
- 多种消息格式
- 温度、最大令牌数等参数

## 🔍 故障排查

### 常见问题

1. **JWT过期**
   - 检查 `kilocode.json` 中的JWT是否有效
   - JWT过期时间可在启动时查看

2. **API密钥错误**
   - 确认使用正确的客户端API密钥
   - 检查 `client_api_keys.json` 配置

3. **模型不可用**
   - 检查模型名称是否正确
   - 确认KiloCode账户有权限使用该模型

### 查看日志

服务启动时会显示详细的JWT信息和状态，包括：
- JWT过期时间
- 用户ID
- 有效性状态

## 🔒 安全说明

- JWT令牌包含敏感信息，请妥善保管
- 不要将配置文件提交到公共代码仓库
- 定期更新JWT令牌

## 📝 更新日志

### v1.0.0
- 初始版本
- 支持OpenAI兼容的聊天完成API
- 支持流式和非流式响应
- JWT自动验证和过期检查

---

**注意**: 本项目仅用于学习和研究目的，请遵守KiloCode AI的使用条款。
