# 一键启动脚本说明

## 新建的启动脚本

### 1. KiloCode2API + FRP 启动
**文件**: `kilo2api/start_kilo2api_frp.bat`
- **本地端口**: 8001
- **远程端口**: 8081 (http://*************:8081)
- **API密钥**: `sk-kilo-api-key`

### 2. Ki2API + FRP 启动  
**文件**: `ki2api/start_ki2api_frp.bat`
- **本地端口**: 8989
- **远程端口**: 8989 (http://*************:8989)
- **API密钥**: `kiro2api-key`

### 3. Copilot API + FRP 启动
**文件**: `copilot-api-master/start_copilot_frp.bat`
- **本地端口**: 8002
- **远程端口**: 8002 (http://*************:8002)
- **认证**: GitHub Copilot订阅账号

## 使用方法

### 启动KiloCode2API服务
```bash
cd kilo2api
start_kilo2api_frp.bat
```

### 启动Ki2API服务
```bash
cd ki2api
start_ki2api_frp.bat
```

### 启动Copilot API服务
```bash
cd copilot-api-master
start_copilot_frp.bat
```

## 功能特点

### 所有脚本都包含：
- ✅ 自动启动FRP内网穿透
- ✅ 自动安装依赖
- ✅ 清晰的状态提示
- ✅ 服务停止时自动清理FRP进程
- ✅ 本地和远程访问地址提示

### 注意事项
- Ki2API脚本会自动生成专用的FRP配置文件
- 服务停止时会自动清理临时文件和FRP进程
- 确保*************:7000 FRP服务器正常运行

## 访问测试

### KiloCode2API测试
```bash
# 本地测试
curl -H "Authorization: Bearer sk-kilo-api-key" http://localhost:8001/v1/models

# 远程测试
curl -H "Authorization: Bearer sk-kilo-api-key" http://*************:8081/v1/models
```

### Ki2API测试
```bash
# 本地测试  
curl -H "Authorization: Bearer kiro2api-key" http://localhost:8989/v1/models

# 远程测试
curl -H "Authorization: Bearer kiro2api-key" http://*************:8989/v1/models
```

### Copilot API测试
```bash
# 本地测试
curl http://localhost:8002/v1/models

# 远程测试
curl http://*************:8002/v1/models

# 使用页面
# https://ericc-ch.github.io/copilot-api?endpoint=http://*************:8002/usage
```