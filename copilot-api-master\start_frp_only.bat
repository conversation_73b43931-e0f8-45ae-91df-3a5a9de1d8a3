@echo off
chcp 65001 >nul
title FRP for Copilot API

echo ========================================
echo       FRP Client for Copilot API
echo ========================================
echo.

rem 检查frpc.toml是否存在于当前目录
if not exist .\frpc.toml (
    echo ❌ 错误: 当前目录下未找到 frpc.toml 文件。
    pause
    exit /b
)

echo 🚀 正在后台启动FRP客户端...
echo    连接到服务器: 117.72.11.220:7000
echo    转发规则: 本地 8002 -> 远程 8002
echo.

rem 使用相对路径启动FRP，假设frp程序在上一层目录
start "FRP for Copilot" /min ..\frp_0.52.3_windows_amd64\frpc.exe -c .\frpc.toml

if %errorlevel% neq 0 (
    echo ❌ 错误: 启动frpc.exe失败。请确保路径正确。
    pause
    exit /b
)

timeout /t 2 >nul

echo ✅ FRP客户端已在后台启动。
echo    远程API现已可以通过 http://117.72.11.220:8002 访问。
echo.
echo ℹ️  关闭此窗口不会影响后台运行的FRP进程。
echo    若要停止FRP，请在任务管理器中结束 frpc.exe 进程。
echo.
pause