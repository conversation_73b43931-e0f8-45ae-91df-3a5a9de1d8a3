@echo off
title TurnstileAI + FRP Launcher

echo ========================================
echo    TurnstileAI + FRP Launcher
echo ========================================
echo.

echo Starting services...
echo    - Turnstile Solver API (port: 5000)  
echo    - TenBinAI OpenAI API (port: 8003)
echo    - FRP tunnel (remote port: 8003)
echo    - Server: *************:7000
echo.

echo Access URLs:
echo    - Local: http://localhost:8003/v1
echo    - Remote: http://*************:8003/v1
echo    - API Key: sk-ellchan
echo.

echo Generating FRP config...
(
echo # TurnstileAI FRP config
echo serverAddr = "*************"
echo serverPort = 7000
echo.
echo # Log config
echo log.to = "./frpc_turnstile.log"
echo log.level = "info"
echo.
echo # TurnstileAI HTTP tunnel
echo [[proxies]]
echo name = "turnstile-ai-http"
echo type = "tcp"
echo localIP = "127.0.0.1"
echo localPort = 8003
echo remotePort = 8003
) > "..\frp_0.52.3_windows_amd64\frpc_turnstile.toml"

echo Starting FRP client...
start "FRP Client - TurnstileAI" /min "..\frp_0.52.3_windows_amd64\frpc.exe" -c "..\frp_0.52.3_windows_amd64\frpc_turnstile.toml"

timeout /t 2 >nul

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Checking dependencies...
pip list 2>nul | findstr "fastapi uvicorn quart websocket-client" >nul
if %errorlevel% neq 0 (
    echo Installing dependencies...
    pip install -r requirements.txt
)

echo.
echo Starting Turnstile Solver service (port 5000)...
start "Turnstile Solver" python api_solver.py --port 5000

timeout /t 3 >nul

echo Starting TenBinAI API service (port 8003)...
echo    Service URL: http://localhost:8003/v1
echo    FRP Tunnel: http://*************:8003/v1
echo    Captcha Service: http://localhost:5000
echo.

python main.py

echo.
echo Service stopped, cleaning up...
echo Closing related processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im frpc.exe >nul 2>&1
del "..\frp_0.52.3_windows_amd64\frpc_turnstile.toml" >nul 2>&1

pause