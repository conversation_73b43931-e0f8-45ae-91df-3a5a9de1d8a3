@echo off
chcp 65001 >nul
title KiloCode AI + FRP 一键启动

echo ========================================
echo    KiloCode AI + FRP 一键启动器
echo ========================================
echo.

echo 🚀 正在启动服务组合...
echo    • KiloCode AI API (端口: 8001)
echo    • FRP 内网穿透 (远程端口: 8081)
echo    • 服务器: *************:7000
echo.

echo 📋 启动后访问地址:
echo    • 本地: http://localhost:8001/v1
echo    • 远程: http://*************:8081/v1
echo    • API密钥: sk-kilo-api-key
echo.

echo ⚡ 正在启动FRP客户端...
start "FRP Client" /min "..\frp_0.52.3_windows_amd64\frpc.exe" -c "..\frp_0.52.3_windows_amd64\frpc.toml"

timeout /t 3 >nul

echo 🔧 激活虚拟环境...
call ..\venv\Scripts\activate.bat 2>nul

echo 📦 检查并安装依赖...
pip list 2>nul | findstr "fastapi uvicorn httpx pydantic" >nul
if %errorlevel% neq 0 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
)

echo.
echo 🚀 启动KiloCode AI服务...
echo    服务地址: http://localhost:8001/v1
echo    FRP转发: http://*************:8081/v1
echo.

python main.py

echo.
echo ⚠️  服务已停止，正在关闭FRP连接...
taskkill /f /im frpc.exe >nul 2>&1

pause