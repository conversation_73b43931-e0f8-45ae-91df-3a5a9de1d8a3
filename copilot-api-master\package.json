{"name": "copilot-api", "version": "0.5.4", "description": "Turn GitHub Copilot into OpenAI/Anthropic API compatible server. Usable with Claude Code!", "keywords": ["proxy", "github-copilot", "openai-compatible"], "homepage": "https://github.com/ericc-ch/copilot-api", "bugs": "https://github.com/ericc-ch/copilot-api/issues", "repository": {"type": "git", "url": "git+https://github.com/ericc-ch/copilot-api.git"}, "author": "<PERSON><PERSON> <<EMAIL>>", "type": "module", "bin": {"copilot-api": "./dist/main.js"}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "bun run --watch ./src/main.ts", "knip": "knip-bun", "lint": "eslint . --cache", "prepack": "bun run build", "prepare": "simple-git-hooks", "release": "bumpp && bun publish --access public", "start": "NODE_ENV=production bun run ./src/main.ts", "typecheck": "tsc"}, "simple-git-hooks": {"pre-commit": "bunx lint-staged"}, "lint-staged": {"*": "bun run lint --fix"}, "dependencies": {"citty": "^0.1.6", "clipboardy": "^4.0.0", "consola": "^3.4.2", "fetch-event-stream": "^0.1.5", "gpt-tokenizer": "^3.0.1", "hono": "^4.8.5", "srvx": "^0.8.2", "tiny-invariant": "^1.3.3"}, "devDependencies": {"@echristian/eslint-config": "^0.0.49", "@types/bun": "^1.2.19", "bumpp": "^10.2.0", "eslint": "^9.31.0", "knip": "^5.62.0", "lint-staged": "^16.1.2", "prettier-plugin-packagejson": "^2.5.19", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}