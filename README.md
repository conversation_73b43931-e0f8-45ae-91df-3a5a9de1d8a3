# KiloCode AI + FRP 服务

<div align="center">

![版本](https://img.shields.io/badge/版本-1.0.0-blue.svg)
![许可证](https://img.shields.io/badge/许可证-MIT-green.svg)
![Python](https://img.shields.io/badge/Python-3.11+-brightgreen.svg)

</div>

---

> 高性能异步 AI 代理服务，将 KiloCode AI 的大语言模型转换为 OpenAI API 格式，同时提供 FRP 内网穿透服务。

## 📁 项目结构

```
kilocode2api-frp/
├── kilo2api/                    # KiloCode AI API 适配器
│   ├── main.py                  # 主程序（异步服务器 + API 适配器）
│   ├── requirements.txt         # Python 依赖
│   ├── kilocode.json           # KiloCode JWT 配置
│   ├── client_api_keys.json    # 客户端 API 密钥配置
│   ├── models.json             # 可用模型配置
│   ├── start_kilo_api.bat      # Windows 启动脚本
│   └── test_kilo_api.bat       # 测试脚本
├── frp_0.52.3_windows_amd64/   # FRP 内网穿透服务
│   ├── frpc.exe                # FRP 客户端
│   ├── frps.exe                # FRP 服务端
│   ├── frpc.toml              # 客户端配置
│   └── frps.toml              # 服务端配置
└── JetBrains_AI_FRP_配置文档.md  # FRP 配置说明
```

## 🚀 快速开始

### 1. 配置 KiloCode API

#### 配置 KiloCode JWT
创建 `kilo2api/kilocode.json` 文件：
```json
[
    {
        "jwt": "your-kilocode-jwt-here"
    }
]
```

#### 配置客户端密钥
创建 `kilo2api/client_api_keys.json`：
```json
[
  "sk-client-key-1",
  "sk-client-key-2"
]
```

#### 配置可用模型
创建 `kilo2api/models.json`：
```json
[
    "anthropic-claude-3.7-sonnet",
    "anthropic-claude-4-sonnet", 
    "google-chat-gemini-pro-2.5",
    "openai-o4-mini",
    "openai-o3-mini",
    "openai-o3",
    "openai-o1",
    "openai-gpt-4o",
    "anthropic-claude-3.5-sonnet",
    "openai-gpt4.1"
]
```

### 2. 启动 KiloCode API 服务

#### Windows 直接启动
```bash
cd kilo2api
python -m pip install -r requirements.txt
python main.py
```

#### 或使用批处理脚本
```bash
cd kilo2api
start_kilo_api.bat
```

API 服务将在 `http://localhost:8001` 启动

### 3. 配置 FRP 服务

参考 `JetBrains_AI_FRP_配置文档.md` 配置内网穿透服务。

## 🔌 API 接口

### 聊天完成
```http
POST /v1/chat/completions
Authorization: Bearer <client-api-key>
Content-Type: application/json
```

**请求示例：**
```json
{
  "model": "anthropic-claude-3.5-sonnet",
  "messages": [
    {"role": "user", "content": "你好"}
  ],
  "stream": true
}
```

### 模型列表
```http
GET /v1/models
Authorization: Bearer <client-api-key>
```

## 💻 使用示例

### Python + OpenAI SDK
```python
import openai

client = openai.OpenAI(
    api_key="sk-client-key-1",
    base_url="http://localhost:8001/v1"
)

# 流式对话
response = client.chat.completions.create(
    model="anthropic-claude-3.5-sonnet",
    messages=[{"role": "user", "content": "写一首关于春天的诗"}],
    stream=True
)

for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### cURL
```bash
curl -X POST http://localhost:8001/v1/chat/completions \
  -H "Authorization: Bearer sk-client-key-1" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic-claude-3.5-sonnet",
    "messages": [{"role": "user", "content": "你好"}],
    "stream": true
  }'
```

## 🔧 测试服务

运行测试脚本验证API服务：
```bash
cd kilo2api
test_kilo_api.bat
```

## 📊 特性

### KiloCode API 适配器
- **⚡ 高并发异步架构**：基于 httpx + FastAPI，支持数千并发连接
- **🔧 OpenAI 完全兼容**：零修改集成现有 OpenAI 客户端和工具  
- **🔐 JWT 认证管理**：支持 JWT 自动轮询和有效性检查
- **📦 开箱即用**：配置简单，快速启动

### FRP 内网穿透
- **🌐 内网穿透**：将本地API服务暴露到公网
- **🔒 安全连接**：支持多种认证方式
- **📈 高性能**：低延迟的内网穿透解决方案

---

<div align="center">

**如果这个项目对您有帮助，请考虑给个 ⭐ Star！**

</div>