{"Claude-3-Haiku": "AnthropicClaude3Haiku", "Claude-3-Opus": "AnthropicClaude3Opus", "Claude-3-Sonnet": "AnthropicClaude3Sonnet", "Claude-4-Opus": "AnthropicClaude4Opus", "Claude-4-Sonnet": "AnthropicClaude4Sonnet", "Claude-3.5-Haiku": "AnthropicClaude<PERSON><PERSON><PERSON><PERSON>", "Claude-3.5-Sonnet": "AnthropicClaude35Sonnet", "Claude-3.7-Sonnet": "AnthropicClaude37Sonnet", "Claude-3.7-Sonnet-Extended": "AnthropicClaude37SonnetExtended", "DeepSeek-R1": "DeepSeekR1", "DeepSeek-V3": "DeepSeekV3", "Gemini-1.5-Flash": "GeminiFlash15", "Gemini-2.0-Flash": "GeminiFlash20", "Gemini-2.5-Flash": "GeminiFlash25", "Gemini-1.0-Pro": "GeminiPro10", "Gemini-1.5-Pro": "GeminiPro15", "Gemini-2.5-Pro": "GeminiPro25", "Llama-3-Sonar-Large-32K-Online": "Llama3SonarLarge32kOnline", "Llama-3.1-Sonar-Large-128K-Chat": "Llama31SonarLarge128kChat", "Llama-3.1-Sonar-Large-128K-Online": "Llama31SonarLarge128kOnline", "Llama-3-70B-Instruct": "Llama370bInstruct", "Mixtral-8x7B-Instruct": "Mixtral8x7bInstruct", "GPT-4": "OpenAIgpt4", "GPT-4o-mini": "OpenAIgpt4oMini", "GPT-3.5-Turbo": "OpenAIgpt35", "GPT-4.1": "OpenAIgpt41", "GPT-4.1-mini": "OpenAIgpt41Mini", "GPT-4.1-nano": "OpenAIgpt41Nano", "GPT-4.5": "OpenAIgpt45", "o1": "OpenAIo1", "o1-mini": "OpenAIo1Mini", "o3": "OpenAIo3", "o3-mini": "OpenAIo3Mini", "o4-mini": "OpenAIo4Mini", "Perplexity-Sonar": "PerplexitySonar", "Plamo-1.0-Prime": "Plamo10Prime"}