@echo off
chcp 65001 >nul
title Ki2API + FRP 一键启动

echo ========================================
echo     Ki2API + FRP 一键启动器
echo ========================================
echo.

echo 🚀 正在启动服务组合...
echo    • Ki2API Claude Sonnet 4 (端口: 8989)
echo    • FRP 内网穿透 (远程端口: 8989)
echo    • 服务器: *************:7000
echo.

echo 📋 启动后访问地址:
echo    • 本地: http://localhost:8989/v1
echo    • 远程: http://*************:8989/v1
echo    • API密钥: kiro2api-key
echo.

echo ⚡ 正在生成临时FRP配置...
(
echo # Ki2API专用FRP配置
echo serverAddr = "*************"
echo serverPort = 7000
echo.
echo # 日志配置
echo log.to = "./frpc_ki2api.log"
echo log.level = "info"
echo.
echo # Ki2API HTTP隧道
echo [[proxies]]
echo name = "ki2api-http"
echo type = "tcp"
echo localIP = "127.0.0.1"
echo localPort = 8989
echo remotePort = 8989
) > "..\frp_0.52.3_windows_amd64\frpc_ki2api.toml"

echo ⚡ 正在启动FRP客户端...
start "FRP Client - Ki2API" /min "..\frp_0.52.3_windows_amd64\frpc.exe" -c "..\frp_0.52.3_windows_amd64\frpc_ki2api.toml"

timeout /t 3 >nul

echo 📦 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Docker，请先安装Docker Desktop
    pause
    exit /b 1
)

echo 🐳 启动Ki2API Docker服务...
echo    本地服务: http://localhost:8989/v1
echo    FRP转发: http://*************:8989/v1
echo.

docker-compose up

echo.
echo ⚠️  服务已停止，正在清理...
docker-compose down
taskkill /f /im frpc.exe >nul 2>&1
del "..\frp_0.52.3_windows_amd64\frpc_ki2api.toml" >nul 2>&1

pause