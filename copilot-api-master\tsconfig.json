{"compilerOptions": {"target": "ESNext", "lib": ["ESNext"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "moduleDetection": "force", "erasableSyntaxOnly": true, "verbatimModuleSyntax": true, "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"]}}}